import 'package:realm/realm.dart';
import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';

import '../../features/home/<USER>/models/task_detail_model.dart';

class RealmDatabase {
  static RealmDatabase? _instance;
  late Realm _realm;

  RealmDatabase._() {
    final config = Configuration.local(
      [
        // TaskDetail related
        TaskDetailModel.schema,
        PhotoFolderModel.schema,
        SignatureFolderModel.schema,
        FormModel.schema,
        QuestionAnswerModel.schema,
        PosItemModel.schema,
        DocumentModel.schema,
        TaskalertModel.schema,
        TaskmemberModel.schema,
        FollowupTaskModel.schema,
        StocktakeModel.schema,
        PhotoModel.schema,
        SignatureModel.schema,
        QuestionModel.schema,
        QuestionPartModel.schema,
        MeasurementModel.schema,
        MeasurementOptionModel.schema,
        MeasurementConditionModel.schema,
        MeasurementValidationModel.schema,
        MeasurementPhototypesDeprecatedModel.schema,
        QuestionConditionModel.schema,
        PhotoTagsTModel.schema,
        CommentTypeModel.schema,
        FileElementModel.schema,
        // CalendarInfo related
        CalendarInfoModel.schema,
      ],
      schemaVersion: 3,
    );
    _realm = Realm(config);
  }

  static RealmDatabase get instance {
    _instance ??= RealmDatabase._();
    return _instance!;
  }

  Realm get realm => _realm;

  void close() {
    _realm.close();
  }
}
