import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/form_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

@RoutePage()
class FormPage extends StatefulWidget {
  final entities.TaskDetail task;

  const FormPage({
    super.key,
    required this.task,
  });

  @override
  State<FormPage> createState() => _FormPageState();
}

class _FormPageState extends State<FormPage> with RouteAware {
  // Get forms from the task
  List<entities.Form>? get formItems => widget.task.forms;

  // Cache for progress data to avoid recalculating on every build
  final Map<int, Map<String, dynamic>> _progressCache = {};

  @override
  void initState() {
    super.initState();
    // Initial load of progress data
    _refreshAllProgress();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // Refresh progress when returning from child pages
    _refreshAllProgress();
  }

  /// Refresh progress data for all forms by clearing cache and triggering rebuild
  void _refreshAllProgress() {
    _progressCache.clear();
    if (mounted) {
      setState(() {});
    }
  }

  /// Calculate dynamic progress for a specific form based on database data
  Map<String, dynamic> _calculateFormProgress(entities.Form form) {
    final formId = form.formId?.toInt();
    if (formId == null) {
      return {'progress': 0.0, 'progressText': '0 of 0'};
    }

    // Check cache first
    if (_progressCache.containsKey(formId)) {
      return _progressCache[formId]!;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.task.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        final result = {'progress': 0.0, 'progressText': '0 of 0'};
        _progressCache[formId] = result;
        return result;
      }

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((f) => f.formId == formId).firstOrNull;

      if (formModel == null) {
        final result = {'progress': 0.0, 'progressText': '0 of 0'};
        _progressCache[formId] = result;
        return result;
      }

      // Get all questions for this form
      final questions = form.questions ?? [];
      if (questions.isEmpty) {
        final result = {'progress': 0.0, 'progressText': '0 of 0'};
        _progressCache[formId] = result;
        return result;
      }

      int totalQuestions = questions.length;
      int completedQuestions = 0;

      // Check completion status for each question
      for (final question in questions) {
        if (_isQuestionCompleted(question, formModel)) {
          completedQuestions++;
        }
      }

      double progress =
          totalQuestions > 0 ? completedQuestions / totalQuestions : 0.0;
      String progressText = '$completedQuestions of $totalQuestions';

      final result = {'progress': progress, 'progressText': progressText};
      _progressCache[formId] = result;
      return result;
    } catch (e) {
      final result = {'progress': 0.0, 'progressText': '0 of 0'};
      _progressCache[formId] = result;
      return result;
    }
  }

  /// Check if a question is completed based on saved answers in the database
  bool _isQuestionCompleted(entities.Question question, FormModel formModel) {
    final questionId = question.questionId?.toInt();
    if (questionId == null) return false;

    // Get all saved answers for this question
    final savedAnswers = formModel.questionAnswers
        .where((answer) => answer.questionId == questionId)
        .toList();

    if (savedAnswers.isEmpty) return false;

    // For multi-questions, check if there are any completed instances with actual measurement data
    if (question.isMulti == true) {
      return savedAnswers.any((answer) => _hasActualMeasurementData(answer));
    }

    // For regular questions, check if all required measurements have valid answers
    final measurements = question.measurements ?? [];

    if (measurements.isEmpty) {
      // If no measurements, just check if there are any saved answers
      return savedAnswers.isNotEmpty;
    }

    // Check if all required measurements are completed
    int requiredMeasurements = 0;
    int completedMeasurements = 0;

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      // Check if this measurement is required
      final isRequired = _isMeasurementRequired(measurement);
      if (isRequired) {
        requiredMeasurements++;

        // Check if this measurement has a valid answer
        final hasValidAnswer = savedAnswers.any((answer) =>
            answer.measurementId == measurement.measurementId?.toInt() &&
            _hasActualMeasurementData(answer));

        if (hasValidAnswer) {
          completedMeasurements++;
        }
      }
    }

    // If no required measurements, check if any measurement has data
    if (requiredMeasurements == 0) {
      return savedAnswers.any((answer) => _hasActualMeasurementData(answer));
    }

    // All required measurements must be completed
    return completedMeasurements == requiredMeasurements;
  }

  /// Check if a measurement is required based on measurement_validations array
  bool _isMeasurementRequired(entities.Measurement measurement) {
    final validations = measurement.measurementValidations ?? [];

    for (final validation in validations) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Check if a QuestionAnswer has actual measurement data (not just selection data)
  bool _hasActualMeasurementData(QuestionAnswerModel answer) {
    // Check if the answer has actual measurement data
    if (answer.measurementId == null) {
      return false; // No measurement ID means it's just a selection entry
    }

    // Check if it has any measurement result data
    final hasTextResult = answer.measurementTextResult != null &&
        answer.measurementTextResult!.isNotEmpty;
    final hasOptionId = answer.measurementOptionId != null;
    final hasOptionIds = answer.measurementOptionIds != null &&
        answer.measurementOptionIds!.isNotEmpty;

    return hasTextResult || hasOptionId || hasOptionIds;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Forms',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            formItems == null || formItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No forms available for this task',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: formItems!.length,
                    itemBuilder: (context, index) {
                      final form = formItems![index];

                      // Calculate dynamic progress based on database data
                      final progressData = _calculateFormProgress(form);
                      final progress = progressData['progress'] as double;
                      final progressText =
                          progressData['progressText'] as String;

                      bool isMandatory = form.isMandatory ?? false;

                      return GestureDetector(
                        onTap: () async {
                          if (form.isVisionForm == true) {
                            await context.router.push(
                                WebBrowserRoute(url: form.visionFormUrl ?? ''));
                          } else {
                            // Navigate to QuestionPage and refresh progress on return
                            await context.router.push(QuestionRoute(
                              form: form,
                              taskId: widget.task.taskId,
                            ));
                          }
                          // Refresh progress after returning from navigation
                          _refreshAllProgress();
                        },
                        child: FormCard(
                          title: form.formName ?? 'Unnamed Form',
                          progress: progress,
                          progressText: progressText,
                          width: 1.0, // Full width for list view
                          isMandatory: isMandatory,
                          isVisionForm: form.isVisionForm ?? false,
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(8),
          ],
        ),
      ),
    );
  }
}
